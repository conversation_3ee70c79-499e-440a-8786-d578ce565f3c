name: tubewell_water_billing_app
description: "A Flutter application for managing tubewell water billing."
publish_to: 'none'
version: 3.0.0+3

environment:
  sdk: ^3.6.1

dependencies:
  flutter:
    sdk: flutter
  cupertino_icons: ^1.0.8

  # Database
  sqflite: ^2.3.2
  path_provider: ^2.1.2
  path: ^1.8.3

  # Utilities
  intl: ^0.20.2
  share_plus: ^11.0.0
  shared_preferences: ^2.2.2
  file_picker: ^10.1.7
  permission_handler: ^12.0.0+1
  uuid: ^4.3.3
  logger: ^2.0.2+1
  synchronized: ^3.1.0

  # PDF Generation
  pdf: ^3.10.7
  printing: ^5.11.1
  open_file: ^3.3.2
  provider: ^6.1.5

  # Charts and Graphs
  fl_chart: ^0.66.2

  # HTTP client
  http: ^1.4.0

  # Messaging
  url_launcher: ^6.2.5

  # UI Components
  flutter_colorpicker: ^1.0.3

  # Image optimization
  flutter_image_compress: ^2.1.0

  # Background tasks (temporarily disabled for faster build)
  # workmanager: ^0.5.2

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^5.0.0
  build_runner: ^2.4.15
  flutter_launcher_icons: ^0.14.3
  flutter_native_splash: ^2.3.10

flutter:
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/images/

# Flutter Launcher Icons configuration
flutter_launcher_icons:
  android: "launcher_icon"
  ios: true
  image_path: "assets/images/logo.png"
  min_sdk_android: 21 # android min sdk min:16, default 21
  adaptive_icon_background: "#FFFFFF"
  adaptive_icon_foreground: "assets/images/logo_foreground.png"
  remove_alpha_ios: true
  web:
    generate: false
  windows:
    generate: false
  macos:
    generate: false

# Flutter Native Splash Screen configuration
flutter_native_splash:
  color: "#FFFFFF"
  image: "assets/images/splash.png"
  color_dark: "#121212"
  image_dark: "assets/images/splash.png"
  android_12:
    image: "assets/images/splash.png"
    icon_background_color: "#FFFFFF"
    image_dark: "assets/images/splash.png"
    icon_background_color_dark: "#121212"
  android: true
  ios: true
  web: false
  fullscreen: true
