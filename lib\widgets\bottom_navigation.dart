import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:tubewell_water_billing_app/screens/customer_list_screen.dart';
import 'package:tubewell_water_billing_app/screens/transactions_screen.dart';
import 'package:tubewell_water_billing_app/screens/payments_screen.dart';
import 'package:tubewell_water_billing_app/screens/expenses_screen.dart';
import 'package:tubewell_water_billing_app/screens/summary_screen.dart';
import 'package:tubewell_water_billing_app/utils/navigation_helper.dart';

/// A scaffold that maintains the state of all bottom navigation tab screens
/// This prevents rebuilding screens when switching tabs, making transitions smooth
class BottomNavigation extends StatefulWidget {
  final int initialIndex;

  const BottomNavigation({
    super.key,
    this.initialIndex = 0,
  });

  @override
  State<BottomNavigation> createState() => _BottomNavigationState();
}

class _BottomNavigationState extends State<BottomNavigation> {
  late int _currentIndex;

  // Keep all the screens in memory
  final List<Widget> _screens = [
    const SummaryScreen(),
    const TransactionsScreen(),
    const CustomerListScreen(),
    const PaymentsScreen(),
    const ExpensesScreen(),
  ];

  // Track history to handle back button
  final List<int> _navigationHistory = [];

  @override
  void initState() {
    super.initState();
    _currentIndex = widget.initialIndex;
    _navigationHistory.add(_currentIndex);
  }

  void _selectTab(int index) {
    if (_currentIndex == index) return;

    setState(() {
      _currentIndex = index;
      // Add to history for back button handling
      _navigationHistory.add(index);
    });
  }

  // Handle back button press with improved animation
  Future<bool> _onWillPop() async {
    if (_navigationHistory.length <= 1) {
      // Only one item in history, show exit confirmation
      final shouldExit = await showDialog<bool>(
        context: context,
        builder: (context) => AlertDialog(
          title: const Text('Exit App'),
          content: const Text('Are you sure you want to exit the app?'),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: const Text('No'),
            ),
            TextButton(
              onPressed: () => Navigator.of(context).pop(true),
              style: TextButton.styleFrom(
                foregroundColor: Colors.red,
              ),
              child: const Text('Yes'),
            ),
          ],
        ),
      ) ?? false;

      return shouldExit;
    }

    // Remove current page from history
    _navigationHistory.removeLast();

    // Navigate to previous page with smooth transition
    setState(() {
      _currentIndex = _navigationHistory.last;
    });

    // Don't pop the actual navigator
    return false;
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false,
      onPopInvokedWithResult: (didPop, result) async {
        if (didPop) return;
        final shouldPop = await _onWillPop();
        if (shouldPop && context.mounted) {
          // Actually exit the app
          NavigationHelper.exitApp();
        }
      },
      child: Scaffold(
        body: AnimatedSwitcher(
          duration: const Duration(milliseconds: 200),
          switchInCurve: Curves.easeOutCubic,
          switchOutCurve: Curves.easeInCubic,
          transitionBuilder: (Widget child, Animation<double> animation) {
            // Use only fade transition for smoother transitions without vibration
            return FadeTransition(
              opacity: animation,
              child: child,
            );
          },
          layoutBuilder: (Widget? currentChild, List<Widget> previousChildren) {
            return Stack(
              alignment: Alignment.center,
              children: <Widget>[
                ...previousChildren,
                if (currentChild != null) currentChild,
              ],
            );
          },
          child: KeyedSubtree(
            key: ValueKey<int>(_currentIndex),
            child: _screens[_currentIndex],
          ),
        ),
        bottomNavigationBar: BottomNavigationBar(
          currentIndex: _currentIndex,
          selectedItemColor: const Color(0xFF2E7D32),
          unselectedItemColor: null,
          showUnselectedLabels: true,
          type: BottomNavigationBarType.fixed,
          items: [
            BottomNavigationBarItem(
              icon: Icon(
                Icons.home,
                color: _currentIndex == 0
                    ? const Color(0xFF2E7D32)
                    : Colors.blue.shade700,
              ),
              label: 'Home',
            ),
            BottomNavigationBarItem(
              icon: Icon(
                Icons.receipt_long,
                color:
                    _currentIndex == 1 ? const Color(0xFF2E7D32) : Colors.pink,
              ),
              label: 'Bills',
            ),
            BottomNavigationBarItem(
              icon: Icon(
                Icons.people,
                color: _currentIndex == 2
                    ? const Color(0xFF2E7D32)
                    : Colors.purple,
              ),
              label: 'Customers',
            ),
            BottomNavigationBarItem(
              icon: Icon(
                Icons.payment,
                color:
                    _currentIndex == 3 ? const Color(0xFF2E7D32) : Colors.red,
              ),
              label: 'Payments',
            ),
            BottomNavigationBarItem(
              icon: Icon(
                Icons.account_balance_wallet,
                color: _currentIndex == 4
                    ? const Color(0xFF2E7D32)
                    : Colors.indigo,
              ),
              label: 'Expenses',
            ),
          ],
          onTap: _selectTab,
        ),
      ),
    );
  }
}
