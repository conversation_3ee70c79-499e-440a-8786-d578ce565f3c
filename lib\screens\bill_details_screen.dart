import 'dart:async';
import 'package:flutter/material.dart';
import 'package:tubewell_water_billing_app/models/bill.dart';
import 'package:tubewell_water_billing_app/models/customer.dart';
import 'package:tubewell_water_billing_app/models/payment.dart';
import 'package:tubewell_water_billing_app/models/currency.dart';
import 'package:tubewell_water_billing_app/services/database_service.dart';
import 'package:tubewell_water_billing_app/services/payment_service.dart';
import 'package:tubewell_water_billing_app/widgets/app_bar_widget.dart';
import 'package:intl/intl.dart';
import 'package:tubewell_water_billing_app/forms/transaction_form_screen.dart';
import 'package:tubewell_water_billing_app/screens/customer_detail_screen.dart';
import 'package:tubewell_water_billing_app/forms/auto_payment_form_screen.dart';

import 'package:tubewell_water_billing_app/services/pdf_services/universal_pdf_service.dart';
import 'package:tubewell_water_billing_app/services/pdf_services/pdf_settings.dart';

import 'package:tubewell_water_billing_app/services/currency_service.dart';
import 'package:tubewell_water_billing_app/services/data_change_notifier_service.dart';
import 'package:tubewell_water_billing_app/widgets/reminder_dialog.dart';

class BillDetailsScreen extends StatefulWidget {
  final Bill bill;
  final Customer? customer;

  const BillDetailsScreen({
    super.key,
    required this.bill,
    required this.customer,
  });

  @override
  State<BillDetailsScreen> createState() => _BillDetailsScreenState();
}

class _BillDetailsScreenState extends State<BillDetailsScreen> {
  late Bill _bill;
  bool _isLoading = false;
  List<Payment> _billPayments = [];

  // Stream subscription for currency changes
  late final StreamSubscription<Currency> _currencySubscription;

  @override
  void initState() {
    super.initState();
    _bill = widget.bill;
    _loadBillPayments();

    // Listen for currency changes
    _currencySubscription = CurrencyService.onCurrencyChanged.listen((_) {
      // When currency changes, refresh the UI
      if (mounted) {
        setState(() {
          // Just trigger a rebuild to update currency formatting
        });
      }
    });
  }

  @override
  void dispose() {
    _currencySubscription.cancel();
    super.dispose();
  }

  Future<void> _loadBillPayments() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // Get all payments for this customer, not just the ones linked to this bill
      // This allows us to allocate payments across multiple bills
      final customerPayments =
          await DatabaseService.getPaymentsByCustomer(widget.bill.customerId);

      // Also get payments directly linked to this bill for display in the UI
      final directPayments = await DatabaseService.getPaymentsByBill(_bill.id);

      // Create a fresh copy of the bill
      final updatedBill = _bill.clone();

      // Update payment status based on all customer payments, not just direct links
      // This allows payments to be allocated across multiple bills
      updatedBill.updatePaymentStatus(customerPayments);

      // Save the updated bill to ensure database is in sync
      await DatabaseService.updateBill(updatedBill);

      // Now get the bill with updated payment status
      final refreshedBill =
          await DatabaseService.getBillWithPaymentStatus(_bill.id);

      if (refreshedBill != null) {
        setState(() {
          _bill = refreshedBill;
          // For the UI, we still show only the directly linked payments
          _billPayments = directPayments;
          _isLoading = false;
        });
      } else {
        setState(() {
          _isLoading = false;
        });
      }
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error loading bill data: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.day}/${dateTime.month}/${dateTime.year} ${dateTime.hour}:${dateTime.minute.toString().padLeft(2, '0')}';
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              label,
              style: const TextStyle(
                fontWeight: FontWeight.bold,
                color: Colors.grey,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: TubewellAppBar(
        title: 'Bill Details',
        showBackButton: true,
        showPdfOption: true,
        onPdfPressed: () => _generateAndDownloadPdf(),
        pdfData: {
          'bill': _bill,
          'customer': widget.customer,
          'payments': _billPayments,
        },
        actions: [
          IconButton(
            icon: const Icon(Icons.picture_as_pdf),
            onPressed: () => _generateAndDownloadPdf(),
            tooltip: 'Download PDF',
          ),
          IconButton(
            icon: const Icon(Icons.sync),
            onPressed: () => _fixBillStatus(),
            tooltip: 'Fix Payment Status',
          ),
          IconButton(
            icon: const Icon(Icons.edit),
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => TransactionFormScreen(
                    existingBill: _bill,
                  ),
                ),
              ).then((result) {
                if (result == true) {
                  // Refresh bill data using getBillWithPaymentStatus
                  _loadBillData();
                }
              });
            },
            tooltip: 'Edit Bill',
          ),
          IconButton(
            icon: const Icon(Icons.delete),
            onPressed: () => _showDeleteConfirmation(),
            tooltip: 'Delete Bill',
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16.0),
              child: _buildDetailsView(),
            ),
      bottomNavigationBar: _bill.isPaid
          ? null
          : Padding(
              padding: const EdgeInsets.fromLTRB(16.0, 8.0, 16.0, 36.0),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Row(
                    children: [
                      Expanded(
                        child: ElevatedButton.icon(
                          onPressed: () => _showReminderDialog(),
                          icon: const Icon(Icons.notifications_active),
                          label: const Text('Send Reminder'),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.purple.shade700,
                            foregroundColor: Colors.white,
                          ),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  Row(
                    children: [
                      Expanded(
                        child: ElevatedButton(
                          onPressed: () => _navigateToPaymentForm(),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: const Color(0xFF2E7D32),
                            foregroundColor: Colors.white,
                          ),
                          child: const Text('Record Payment'),
                        ),
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: ElevatedButton(
                          onPressed: () => _markBillAsPaid(),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.blue.shade700,
                            foregroundColor: Colors.white,
                          ),
                          child: const Text('Mark as Paid'),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
    );
  }

  void _navigateToPaymentForm() {
    // Don't show payment form if bill is already paid
    if (_bill.isPaid) return;

    // Show payment sheet for this bill
    _showBillPaymentSheet();
  }

  void _showBillPaymentSheet() {
    final formKey = GlobalKey<FormState>();
    final amountController = TextEditingController();
    final remarksController = TextEditingController();
    DateTime paymentDate = DateTime.now();
    String paymentMethod = 'Cash';

    // Set initial amount to the remaining amount due
    double remainingAmount = _bill.outstandingAmount;
    amountController.text = remainingAmount.toStringAsFixed(2);

    final paymentMethods = ['Cash', 'Bank Transfer', 'Check', 'UPI', 'Other'];

    bool isProcessing = false;

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (BuildContext context) {
        return StatefulBuilder(builder: (context, setState) {
          return Container(
            padding: EdgeInsets.only(
              bottom: MediaQuery.of(context).viewInsets.bottom,
            ),
            decoration: const BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(20),
                topRight: Radius.circular(20),
              ),
            ),
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Form(
                key: formKey,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // Header
                    Center(
                      child: Container(
                        width: 50,
                        height: 5,
                        decoration: BoxDecoration(
                          color: Colors.grey.shade300,
                          borderRadius: BorderRadius.circular(10),
                        ),
                      ),
                    ),
                    const SizedBox(height: 16),
                    Row(
                      children: [
                        Icon(Icons.payment, color: Colors.green.shade700),
                        const SizedBox(width: 8),
                        Text(
                          'Payment for Bill #${_bill.id}',
                          style:
                              Theme.of(context).textTheme.titleLarge?.copyWith(
                                    fontWeight: FontWeight.bold,
                                  ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    const Divider(),

                    // Bill Summary
                    Container(
                      margin: const EdgeInsets.symmetric(vertical: 16),
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: Colors.blue.shade50,
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(color: Colors.blue.shade200),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Icon(Icons.info_outline,
                                  size: 16, color: Colors.blue.shade700),
                              const SizedBox(width: 8),
                              Text(
                                'Bill Summary',
                                style: TextStyle(
                                  fontWeight: FontWeight.bold,
                                  color: Colors.blue.shade900,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 8),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              const Text('Total Amount:'),
                              Text(
                                CurrencyService.formatCurrency(_bill.amount,
                                    decimalPlaces: 2),
                                style: const TextStyle(
                                    fontWeight: FontWeight.bold),
                              ),
                            ],
                          ),
                          if (_bill.isPartiallyPaid) ...[
                            const SizedBox(height: 4),
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                const Text('Already Paid:'),
                                Text(
                                  CurrencyService.formatCurrency(
                                      _bill.partialAmount ?? 0,
                                      decimalPlaces: 2),
                                  style: TextStyle(
                                    fontWeight: FontWeight.bold,
                                    color: Colors.green.shade700,
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 4),
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                const Text(
                                  'Remaining:',
                                  style: TextStyle(fontWeight: FontWeight.bold),
                                ),
                                Text(
                                  CurrencyService.formatCurrency(
                                      _bill.outstandingAmount,
                                      decimalPlaces: 2),
                                  style: TextStyle(
                                    fontWeight: FontWeight.bold,
                                    color: Colors.red.shade700,
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ],
                      ),
                    ),

                    // Payment Date
                    ListTile(
                      contentPadding: EdgeInsets.zero,
                      title: const Text('Payment Date'),
                      subtitle:
                          Text(DateFormat('dd MMM yyyy').format(paymentDate)),
                      trailing: ElevatedButton.icon(
                        icon: const Icon(Icons.calendar_today, size: 16),
                        label: const Text('Change'),
                        onPressed: () async {
                          final DateTime? picked = await showDatePicker(
                            context: context,
                            initialDate: paymentDate,
                            firstDate: DateTime(2020),
                            lastDate:
                                DateTime.now().add(const Duration(days: 1)),
                          );
                          if (picked != null && picked != paymentDate) {
                            setState(() {
                              paymentDate = picked;
                            });
                          }
                        },
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.blue.shade50,
                          foregroundColor: Colors.blue.shade700,
                        ),
                      ),
                    ),

                    // Payment Method
                    DropdownButtonFormField<String>(
                      decoration: InputDecoration(
                        labelText: 'Payment Method',
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                        prefixIcon: Icon(Icons.credit_card,
                            color: Colors.blue.shade700),
                      ),
                      value: paymentMethod,
                      items: paymentMethods.map((method) {
                        return DropdownMenuItem<String>(
                          value: method,
                          child: Text(method),
                        );
                      }).toList(),
                      onChanged: (value) {
                        if (value != null) {
                          setState(() {
                            paymentMethod = value;
                          });
                        }
                      },
                    ),
                    const SizedBox(height: 16),

                    // Amount
                    TextFormField(
                      controller: amountController,
                      decoration: InputDecoration(
                        labelText: 'Amount',
                        prefixText:
                            '${CurrencyService.currentCurrency.symbol} ',
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                        prefixIcon: Icon(Icons.attach_money,
                            color: Colors.green.shade700),
                        helperText: 'Enter the payment amount',
                      ),
                      keyboardType: TextInputType.number,
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'Please enter an amount';
                        }
                        final amount = double.tryParse(value);
                        if (amount == null) {
                          return 'Please enter a valid amount';
                        }
                        if (amount <= 0) {
                          return 'Amount must be greater than zero';
                        }
                        return null;
                      },
                    ),
                    const SizedBox(height: 16),

                    // Remarks
                    TextFormField(
                      controller: remarksController,
                      decoration: InputDecoration(
                        labelText: 'Remarks (Optional)',
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                        prefixIcon:
                            Icon(Icons.note, color: Colors.orange.shade700),
                      ),
                      maxLines: 2,
                    ),
                    const SizedBox(height: 24),

                    // Submit Button
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton(
                        onPressed: isProcessing
                            ? null
                            : () async {
                                if (formKey.currentState!.validate()) {
                                  setState(() {
                                    isProcessing = true;
                                  });

                                  try {
                                    // Create remarks with bill reference
                                    String finalRemarks = remarksController
                                            .text.isEmpty
                                        ? 'Payment for Bill #${_bill.id}'
                                        : '${remarksController.text} (Payment for Bill #${_bill.id})';

                                    // Ensure we have a valid double value for amount
                                    double paymentAmount;
                                    try {
                                      paymentAmount = double.parse(
                                          amountController.text.trim());
                                      if (paymentAmount <= 0) {
                                        throw Exception(
                                            "Payment amount must be greater than zero");
                                      }
                                    } catch (e) {
                                      throw Exception(
                                          "Invalid payment amount: ${amountController.text}");
                                    }

                                    // Ensure customer ID is valid
                                    final customerId =
                                        widget.customer?.id ?? _bill.customerId;
                                    if (customerId <= 0) {
                                      throw Exception("Invalid customer ID");
                                    }

                                    // Use process payment which handles all the logic
                                    // Pass the current bill ID to ensure the payment is linked to this bill
                                    final result =
                                        await PaymentService.processPayment(
                                      customerId: customerId,
                                      amount: paymentAmount,
                                      paymentDate: paymentDate,
                                      paymentMethod: paymentMethod,
                                      remarks: finalRemarks,
                                      targetBillId: _bill
                                          .id, // Explicitly link to this bill
                                    );

                                    if (result['success'] != true) {
                                      throw Exception(
                                          result['error'] ?? 'Payment failed');
                                    }

                                    // Success message
                                    if (context.mounted) {
                                      ScaffoldMessenger.of(context)
                                          .showSnackBar(
                                        SnackBar(
                                          content: Text(
                                              'Payment of Rs. ${paymentAmount.toStringAsFixed(2)} recorded successfully'),
                                          backgroundColor: Colors.green,
                                        ),
                                      );

                                      // Notify other screens about the payment change
                                      DataChangeNotifierService()
                                          .notifyDataChanged(
                                              DataChangeType.payment);
                                      // Also notify about bill change since payment affects bill status
                                      DataChangeNotifierService()
                                          .notifyDataChanged(
                                              DataChangeType.bill);

                                      // Close bottom sheet
                                      Navigator.pop(context);

                                      // Refresh bill data and payments
                                      _loadBillData();
                                    }
                                  } catch (e) {
                                    // Show error message
                                    if (context.mounted) {
                                      ScaffoldMessenger.of(context)
                                          .showSnackBar(
                                        SnackBar(
                                          content: Text(
                                              'Error recording payment: $e'),
                                          backgroundColor: Colors.red,
                                        ),
                                      );
                                      setState(() {
                                        isProcessing = false;
                                      });
                                    }
                                  }
                                }
                              },
                        style: ElevatedButton.styleFrom(
                          backgroundColor: const Color(0xFF2E7D32),
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(vertical: 16),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                        ),
                        child: isProcessing
                            ? const SizedBox(
                                width: 24,
                                height: 24,
                                child: CircularProgressIndicator(
                                  color: Colors.white,
                                  strokeWidth: 2,
                                ),
                              )
                            : const Text(
                                'Record Payment',
                                style: TextStyle(
                                    fontSize: 16, fontWeight: FontWeight.bold),
                              ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          );
        });
      },
    ).then((_) {
      // Dispose controllers
      amountController.dispose();
      remarksController.dispose();
    });
  }

  void _loadBillData() {
    setState(() {
      _isLoading = true;
    });

    DatabaseService.getBillWithPaymentStatus(_bill.id).then((updatedBill) {
      if (updatedBill != null) {
        setState(() {
          _bill = updatedBill;
        });
        // Load payments again to display payment history
        _loadBillPayments();
      } else {
        setState(() {
          _isLoading = false;
        });
        // Show error if bill not found
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Could not find bill data'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    }).catchError((error) {
      setState(() {
        _isLoading = false;
      });
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error refreshing bill data: ${error.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    });
  }

  Widget _buildDetailsView() {
    // Calculate original duration values
    int originalMinutes = (_bill.durationHours * 60).round();
    if (_bill.discountTime != null && _bill.discountTime! > 0) {
      originalMinutes += _bill.discountTime!.round();
    }
    int originalHours = originalMinutes ~/ 60;
    int originalMinutesRemainder = originalMinutes % 60;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Status Card
        Card(
          color: _bill.isPaid
              ? Colors.green.shade50
              : (_bill.isPartiallyPaid)
                  ? Colors.blue.shade50
                  : Colors.red.shade50,
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Row(
              children: [
                Icon(
                  _bill.isPaid
                      ? Icons.check_circle
                      : (_bill.isPartiallyPaid)
                          ? Icons.pending_actions
                          : Icons.unpublished,
                  color: _bill.isPaid
                      ? Colors.green
                      : (_bill.isPartiallyPaid)
                          ? Colors.blue
                          : Colors.red,
                  size: 32,
                ),
                const SizedBox(width: 16),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Text(
                          'Bill #${_bill.id}',
                          style: const TextStyle(
                            fontWeight: FontWeight.bold,
                            fontSize: 14,
                          ),
                        ),
                        const SizedBox(width: 8),
                        Container(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 8, vertical: 2),
                          decoration: BoxDecoration(
                            color: Colors.grey.shade200,
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Text(
                            DateFormat('dd MMM yyyy').format(_bill.billDate),
                            style: TextStyle(
                              fontSize: 12,
                              color: Colors.grey.shade800,
                            ),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 4),
                    Text(
                      _bill.isPaid
                          ? 'PAID'
                          : (_bill.isPartiallyPaid)
                              ? 'PARTIALLY PAID'
                              : 'UNPAID',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 18,
                        color: _bill.isPaid
                            ? Colors.green
                            : (_bill.isPartiallyPaid)
                                ? Colors.blue.shade800
                                : Colors.red,
                      ),
                    ),
                    if (_bill.isPaid && _bill.paidDate != null)
                      Text(
                        'Paid on: ${_formatDateTime(_bill.paidDate!)}',
                        style: const TextStyle(fontSize: 12),
                      ),
                    const SizedBox(height: 8),
                    if (_bill.isPaid) ...[
                      Container(
                        padding: const EdgeInsets.symmetric(
                            vertical: 8, horizontal: 12),
                        decoration: BoxDecoration(
                          color: Colors.green.shade100,
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(color: Colors.green.shade300),
                        ),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(
                              'Total Amount Paid:',
                              style: TextStyle(
                                fontSize: 14,
                                color: Colors.green.shade800,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            Text(
                              'Rs. ${_bill.amount.toStringAsFixed(2)}',
                              style: TextStyle(
                                fontSize: 16,
                                color: Colors.green.shade800,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ] else if (!(_bill.isPartiallyPaid)) ...[
                      Container(
                        padding: const EdgeInsets.symmetric(
                            vertical: 8, horizontal: 12),
                        decoration: BoxDecoration(
                          color: Colors.red.shade100,
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(color: Colors.red.shade300),
                        ),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(
                              'Amount Due:',
                              style: TextStyle(
                                fontSize: 14,
                                color: Colors.red.shade800,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            Text(
                              'Rs. ${_bill.amount.toStringAsFixed(2)}',
                              style: TextStyle(
                                fontSize: 16,
                                color: Colors.red.shade800,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ] else ...[
                      Container(
                        padding: const EdgeInsets.symmetric(
                            vertical: 8, horizontal: 12),
                        decoration: BoxDecoration(
                          color: Colors.green.shade100,
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(color: Colors.green.shade300),
                        ),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(
                              'Paid Amount:',
                              style: TextStyle(
                                fontSize: 14,
                                color: Colors.green.shade800,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            Text(
                              'Rs. ${_bill.partialAmount?.toStringAsFixed(2) ?? "0.00"}',
                              style: TextStyle(
                                fontSize: 16,
                                color: Colors.green.shade800,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ],
                        ),
                      ),
                      const SizedBox(height: 8),
                      Container(
                        padding: const EdgeInsets.symmetric(
                            vertical: 8, horizontal: 12),
                        decoration: BoxDecoration(
                          color: Colors.red.shade100,
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(color: Colors.red.shade300),
                        ),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(
                              'Remaining:',
                              style: TextStyle(
                                fontSize: 14,
                                color: Colors.red.shade800,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            Text(
                              'Rs. ${(_bill.amount - (_bill.partialAmount ?? 0)).toStringAsFixed(2)}',
                              style: TextStyle(
                                fontSize: 16,
                                color: Colors.red.shade800,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ],
                ),
              ],
            ),
          ),
        ),
        const SizedBox(height: 24),

        // Customer Info
        Text(
          'Customer Information',
          style: Theme.of(context).textTheme.titleMedium,
        ),
        const Divider(),
        _buildInfoRow('Customer:', widget.customer!.name),
        InkWell(
          onTap: () {
            // Navigate to customer details
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) =>
                    CustomerDetailScreen(customer: widget.customer!),
              ),
            );
          },
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 8.0),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  'View Customer Details',
                  style: TextStyle(
                    color: Colors.blue.shade700,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(width: 8),
                Icon(
                  Icons.arrow_forward,
                  size: 16,
                  color: Colors.blue.shade700,
                ),
              ],
            ),
          ),
        ),
        const SizedBox(height: 16),

        // Water Details
        Text(
          'Water Details',
          style: Theme.of(context).textTheme.titleMedium,
        ),
        const Divider(),
        _buildInfoRow('Bill Date:', _formatDateTime(_bill.billDate)),
        _buildInfoRow('Start Time:', _formatDateTime(_bill.startTime)),
        _buildInfoRow('End Time:', _formatDateTime(_bill.endTime)),

        // Duration with detailed time discount information
        Card(
          elevation: 2,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
          color: Colors.teal.shade50,
          child: Padding(
            padding: const EdgeInsets.all(12.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                if (_bill.discountTime != null && _bill.discountTime! > 0) ...[
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Row(
                        children: [
                          Icon(Icons.timer, color: Colors.blue.shade700),
                          const SizedBox(width: 8),
                          const Text(
                            'Original:',
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                      Text(
                        '$originalHours hours $originalMinutesRemainder minutes',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          color: Colors.blue.shade900,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 4),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Row(
                        children: [
                          Icon(Icons.remove_circle_outline,
                              color: Colors.red.shade700),
                          const SizedBox(width: 8),
                          const Text(
                            'Discount:',
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                      Text(
                        '${_bill.discountTime} minutes',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          color: Colors.red.shade900,
                        ),
                      ),
                    ],
                  ),
                  const Divider(height: 16, thickness: 1),
                ],
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Row(
                      children: [
                        Icon(Icons.timelapse, color: Colors.teal.shade700),
                        const SizedBox(width: 8),
                        const Text(
                          'Final Duration:',
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                    Text(
                      '${_bill.durationHoursWhole} hours ${_bill.durationMinutes} minutes',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        color: Colors.teal.shade900,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
        const SizedBox(height: 16),

        // Billing Details
        Text(
          'Billing Details',
          style: Theme.of(context).textTheme.titleMedium,
        ),
        const Divider(),
        _buildInfoRow(
            'Hourly Rate:', 'Rs. ${_bill.hourlyRate.toStringAsFixed(2)}'),

        // Amount with detailed discount information
        Card(
          elevation: 2,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
          color: Colors.green.shade50,
          child: Padding(
            padding: const EdgeInsets.all(12.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                if (_bill.discountAmount != null &&
                    _bill.discountAmount! > 0) ...[
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Row(
                        children: [
                          Icon(Icons.receipt_long, color: Colors.blue.shade700),
                          const SizedBox(width: 8),
                          const Text(
                            'Original:',
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                      Text(
                        'Rs. ${(_bill.amount + _bill.discountAmount!).toStringAsFixed(2)}',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          color: Colors.blue.shade900,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 4),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Row(
                        children: [
                          Icon(Icons.money_off, color: Colors.red.shade700),
                          const SizedBox(width: 8),
                          const Text(
                            'Discount:',
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                      Text(
                        'Rs. ${_bill.discountAmount!.toStringAsFixed(2)}',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          color: Colors.red.shade900,
                        ),
                      ),
                    ],
                  ),
                  const Divider(height: 16, thickness: 1),
                ],
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Row(
                      children: [
                        Icon(Icons.receipt_long, color: Colors.green.shade700),
                        const SizedBox(width: 8),
                        const Text(
                          'Total Amount:',
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                    Text(
                      'Rs. ${_bill.amount.toStringAsFixed(2)}',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                        color: Colors.green.shade900,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),

        if (_bill.remarks != null && _bill.remarks!.isNotEmpty)
          _buildInfoRow('Remarks:', _bill.remarks!),

        // Payment History Section
        const SizedBox(height: 24),
        Row(
          children: [
            Icon(Icons.payments, color: Colors.blue.shade800),
            const SizedBox(width: 8),
            Text(
              'Payment History',
              style: Theme.of(context).textTheme.titleMedium,
            ),
          ],
        ),
        const Divider(),

        if (_billPayments.isEmpty)
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 16.0),
            child: Center(
              child: Column(
                children: [
                  Icon(Icons.info_outline,
                      size: 40, color: Colors.grey.shade400),
                  const SizedBox(height: 8),
                  Text(
                    'No payments recorded for this bill',
                    style: TextStyle(
                      color: Colors.grey.shade600,
                      fontStyle: FontStyle.italic,
                    ),
                  ),
                ],
              ),
            ),
          )
        else
          ListView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: _billPayments.length,
            itemBuilder: (context, index) {
              final payment = _billPayments[index];
              return Card(
                margin: const EdgeInsets.symmetric(vertical: 8.0),
                color: Colors.blue.shade50,
                child: InkWell(
                  onTap: () => _showPaymentDetails(payment),
                  child: Column(
                    children: [
                      ListTile(
                        leading: const Icon(Icons.payment, color: Colors.blue),
                        title: Text(
                          'Rs. ${payment.amount.toStringAsFixed(2)}',
                          style: const TextStyle(fontWeight: FontWeight.bold),
                        ),
                        subtitle: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Date: ${DateFormat('dd MMM yyyy').format(payment.paymentDate)}',
                            ),
                            Text(
                              'Method: ${getPaymentMethodLabel(payment.paymentMethod)}',
                            ),
                            if (payment.remarks != null &&
                                payment.remarks!.isNotEmpty)
                              Text(
                                'Remarks: ${payment.remarks}',
                                maxLines: 2,
                                overflow: TextOverflow.ellipsis,
                              ),
                          ],
                        ),
                        isThreeLine: payment.remarks != null &&
                            payment.remarks!.isNotEmpty,
                        trailing: PopupMenuButton<String>(
                          icon: const Icon(Icons.more_vert),
                          onSelected: (value) {
                            if (value == 'edit') {
                              _showEditPaymentDialog(payment);
                            } else if (value == 'delete') {
                              _showDeletePaymentConfirmation(payment);
                            }
                          },
                          itemBuilder: (context) => [
                            const PopupMenuItem(
                              value: 'edit',
                              child: Row(
                                children: [
                                  Icon(Icons.edit, size: 18),
                                  SizedBox(width: 8),
                                  Text('Edit Payment'),
                                ],
                              ),
                            ),
                            const PopupMenuItem(
                              value: 'delete',
                              child: Row(
                                children: [
                                  Icon(Icons.delete,
                                      size: 18, color: Colors.red),
                                  SizedBox(width: 8),
                                  Text('Delete Payment',
                                      style: TextStyle(color: Colors.red)),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              );
            },
          ),
      ],
    );
  }

  void _showDeleteConfirmation() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Bill'),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Are you sure you want to delete this bill for ${widget.customer!.name}?',
                style: const TextStyle(fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 8),
              Text('Amount: Rs. ${_bill.amount.toStringAsFixed(0)}'),
              Text('Date: ${DateFormat('yyyy-MM-dd').format(_bill.billDate)}'),
              if (_billPayments.isNotEmpty) ...[
                const SizedBox(height: 16),
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.yellow.shade100,
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.yellow.shade700),
                  ),
                  child: Row(
                    children: [
                      Icon(Icons.warning_amber, color: Colors.orange.shade800),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Warning: This bill has ${_billPayments.length} payment(s)',
                              style: TextStyle(
                                fontWeight: FontWeight.bold,
                                color: Colors.orange.shade800,
                              ),
                            ),
                            Text(
                              'Deleting this bill will also delete these linked payments and update the customer\'s balance accordingly. This action cannot be undone.',
                              style: TextStyle(
                                  fontSize: 12, color: Colors.orange.shade900),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('CANCEL'),
          ),
          TextButton(
            onPressed: () async {
              Navigator.of(context).pop();
              await _deleteBill();
            },
            child: const Text('DELETE', style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );
  }

  Future<void> _deleteBill() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // First handle any payments linked to this bill
      if (_billPayments.isNotEmpty) {
        for (final payment in _billPayments) {
          await PaymentService.deletePaymentAndUpdateBillStatus(payment);
        }
      }

      // Then delete the bill
      await DatabaseService.deleteBill(_bill.id);

      // Notify other screens about the bill and payment changes
      DataChangeNotifierService().notifyDataChanged(DataChangeType.bill);
      if (_billPayments.isNotEmpty) {
        DataChangeNotifierService().notifyDataChanged(DataChangeType.payment);
      }
      // Customer balance is also affected
      DataChangeNotifierService().notifyDataChanged(DataChangeType.customer);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Bill deleted successfully'),
            backgroundColor: Colors.green,
          ),
        );
        Navigator.pop(context); // Return to previous screen
      }
    } catch (e) {
      setState(() {
        _isLoading = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error deleting bill: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _showPaymentDetails(Payment payment) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(Icons.payment, color: Colors.blue.shade700),
            const SizedBox(width: 8),
            Text('Payment #${payment.id}'),
          ],
        ),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              _buildInfoRow(
                  'Amount:', 'Rs. ${payment.amount.toStringAsFixed(2)}'),
              _buildInfoRow('Date:',
                  DateFormat('dd MMM yyyy').format(payment.paymentDate)),
              _buildInfoRow(
                  'Method:', getPaymentMethodLabel(payment.paymentMethod)),
              _buildInfoRow('Linked to Bill:',
                  payment.billId > 0 ? '#${payment.billId}' : 'None (Credit)'),
              if (payment.remarks != null && payment.remarks!.isNotEmpty)
                _buildInfoRow('Remarks:', payment.remarks!),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('CLOSE'),
          ),
        ],
      ),
    );
  }

  void _showEditPaymentDialog(Payment payment) async {
    // Add mounted check early
    if (!mounted) return;

    // First, check if we have the customer object
    Customer? customerObj = widget.customer;

    // If customer is null, try to fetch it from the database
    if (customerObj == null && payment.customerId > 0) {
      customerObj = await DatabaseService.getCustomerById(payment.customerId);
    }

    // If we still don't have a customer, show an error
    if (customerObj == null) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Error: Customer not found'),
            backgroundColor: Colors.red,
          ),
        );
      }
      return;
    }

    // At this point, customerObj is guaranteed to be non-null
    final Customer customer = customerObj;

    // Navigate to payment edit screen
    if (mounted) {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => AutoPaymentFormScreen(
            customer: customer,
            existingPayment: payment,
          ),
        ),
      ).then((result) {
        if (result == true) {
          // Refresh bill data and payments
          _loadBillPayments();
        }
      });
    }
  }

  void _showDeletePaymentConfirmation(Payment payment) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Payment?'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Are you sure you want to delete payment #${payment.id}?'),
            const SizedBox(height: 8),
            Text(
              'Amount: Rs. ${payment.amount.toStringAsFixed(2)}',
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.yellow.shade100,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.yellow.shade700),
              ),
              child: Row(
                children: [
                  Icon(Icons.warning_amber, color: Colors.orange.shade800),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      'This will mark the bill as unpaid or partially paid and update the customer\'s credit balance.',
                      style: TextStyle(
                          fontSize: 12, color: Colors.orange.shade900),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('CANCEL'),
          ),
          TextButton(
            onPressed: () async {
              Navigator.of(context).pop();
              await _deletePayment(payment.id);
            },
            child: const Text('DELETE', style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );
  }

  Future<void> _deletePayment(int paymentId) async {
    try {
      await DatabaseService.deletePayment(paymentId);
      setState(() {
        // Create empty list if null to avoid null check operator issues
        final payments = _billPayments.where((p) => p.id != paymentId).toList();
        _billPayments = payments;
      });

      // Notify other screens about the payment and bill changes
      DataChangeNotifierService().notifyDataChanged(DataChangeType.payment);
      DataChangeNotifierService().notifyDataChanged(DataChangeType.bill);

      // Added mounted check before showing SnackBar
      if (!mounted) return;
      ScaffoldMessenger.of(context).showSnackBar(const SnackBar(
        content: Text('Payment deleted'),
        duration: Duration(seconds: 1),
      ));
    } catch (e) {
      if (!mounted) return;
      ScaffoldMessenger.of(context).showSnackBar(SnackBar(
        content: Text('Error deleting payment: $e'),
        duration: const Duration(seconds: 2),
      ));
    }
  }

  // Method to manually fix bill payment status
  Future<void> _fixBillStatus() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // Get the bill with updated payment status
      final updatedBill =
          await DatabaseService.getBillWithPaymentStatus(_bill.id);
      if (updatedBill == null) {
        throw Exception('Bill not found');
      }

      // Save the updated bill
      await DatabaseService.updateBill(updatedBill);

      // Refresh the UI
      setState(() {
        _bill = updatedBill;
        _isLoading = false;
      });

      if (!mounted) return;
      ScaffoldMessenger.of(context).showSnackBar(const SnackBar(
        content: Text('Bill status updated'),
        duration: Duration(seconds: 1),
      ));
    } catch (e) {
      setState(() {
        _isLoading = false;
      });

      if (!mounted) return;
      ScaffoldMessenger.of(context).showSnackBar(SnackBar(
        content: Text('Error updating bill status: $e'),
        duration: const Duration(seconds: 2),
      ));
    }
  }

  // Generate and download PDF bill
  Future<void> _generateAndDownloadPdf() async {
    // Add mounted check early
    if (!mounted) return;

    try {
      setState(() {
        _isLoading = true;
      });

      // Ensure we have the customer data
      Customer? customer = widget.customer;
      if (customer == null) {
        customer = await DatabaseService.getCustomerById(_bill.customerId);
        if (customer == null) {
          throw Exception('Customer not found');
        }
      }

      // Calculate original duration values for PDF
      int originalMinutes = (_bill.durationHours * 60).round();
      if (_bill.discountTime != null && _bill.discountTime! > 0) {
        originalMinutes += _bill.discountTime!.round();
      }
      int originalHours = originalMinutes ~/ 60;
      int originalMinutesRemainder = originalMinutes % 60;

      // Create enhanced PDF data with detailed information
      final pdfData = {
        'bill': _bill,
        'customer': customer,
        'payments': _billPayments,
        'isBillDetail': true, // Add this flag to identify bill detail PDFs

        // Add detailed timing information
        'timeDetails': {
          'startTime': _bill.startTime,
          'endTime': _bill.endTime,
          'originalDuration': {
            'hours': originalHours,
            'minutes': originalMinutesRemainder,
            'totalMinutes': originalMinutes,
          },
          'timeDiscount': _bill.discountTime ?? 0,
          'finalDuration': {
            'hours': _bill.durationHoursWhole,
            'minutes': _bill.durationMinutes,
            'totalHours': _bill.durationHours,
          },
        },

        // Add detailed billing information
        'billingDetails': {
          'hourlyRate': _bill.hourlyRate,
          'originalAmount': _bill.discountAmount != null ?
              _bill.amount + _bill.discountAmount! : _bill.amount,
          'amountDiscount': _bill.discountAmount ?? 0,
          'finalAmount': _bill.amount,
          'outstandingAmount': _bill.outstandingAmount,
          'paidAmount': _bill.partialAmount ?? (_bill.isPaid ? _bill.amount : 0),
          'paymentStatus': _bill.isPaid ? 'Paid' :
              (_bill.isPartiallyPaid ? 'Partially Paid' : 'Unpaid'),
          'paidDate': _bill.paidDate,
        },
      };

      // Check if widget is still mounted before proceeding
      if (!mounted) return;

      // Use the universal PDF service to generate the PDF with modern design
      await UniversalPdfService.handlePdf(
        context,
        pdfData,
        autoOpen: true,
        showSaveOption: true,
        showShareOption: true,
        pdfSettings: PdfSettings.modernInvoice().copyWith(
          additionalSettings: {
            'footerText': 'Thank you for your business!',
            'reference': 'INV-${_bill.id}',
            'title': 'Water Bill Invoice',
            'showDetailedTimeInformation': true,
            'showDetailedBillingInformation': true,
            'showPaymentHistory': true,
          },
        ),
      );

      if (mounted) {
        setState(() {
          _isLoading = false;
        });
        // Show success message
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('PDF generated successfully'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      setState(() {
        _isLoading = false;
      });

      // Added mounted check before showing SnackBar
      if (!mounted) return;
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error generating PDF: ${e.toString()}'),
          backgroundColor: Colors.red,
          action: SnackBarAction(
            label: 'RETRY',
            onPressed: _generateAndDownloadPdf,
            textColor: Colors.white,
          ),
        ),
      );
    }
  }

  String getPaymentMethodLabel(String? method) {
    if (method == null) {
      return 'Not specified';
    }
    return method;
  }

  // Show reminder dialog for this bill
  void _showReminderDialog() {
    if (widget.customer == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Customer information not available'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    // Don't show reminder dialog for paid bills
    if (_bill.isPaid) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('This bill is already paid'),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }

    showDialog(
      context: context,
      builder: (context) => ReminderDialog(
        customer: widget.customer!,
        bills: [_bill],
        title: 'Send Payment Reminder',
      ),
    );
  }

  // Add this method to mark the bill as paid
  Future<void> _markBillAsPaid() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // Instead of just toggling status, create a payment record
      final customerId = widget.customer?.id ?? _bill.customerId;

      // Create a payment that matches the outstanding amount
      final paymentAmount = _bill.outstandingAmount;

      // Add a remark that this was an auto-generated payment
      final remarks =
          "Auto-generated payment for Bill #${_bill.id} (Marked as Paid)";

      // Use the payment service to process the payment properly
      final result = await PaymentService.processPayment(
        customerId: customerId,
        amount: paymentAmount,
        paymentDate: DateTime.now(),
        paymentMethod: 'Cash', // Default to cash payment
        remarks: remarks,
        targetBillId: _bill.id, // Link directly to this bill
      );

      if (result['success'] != true) {
        throw Exception(result['error'] ?? 'Payment creation failed');
      }

      // Show success message
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Bill marked as paid and payment record created'),
            backgroundColor: Colors.green,
          ),
        );

        // Notify other screens about the payment and bill changes
        DataChangeNotifierService().notifyDataChanged(DataChangeType.payment);
        DataChangeNotifierService().notifyDataChanged(DataChangeType.bill);
      }

      // Refresh bill data to show updated status
      _loadBillData();
    } catch (e) {
      setState(() {
        _isLoading = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}
